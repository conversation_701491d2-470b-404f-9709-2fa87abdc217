{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:62080", "sslPort": 0}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"aws_region": "us-east-1", "ASPNETCORE_ENVIRONMENT": "Local", "aws_country": "us", "aws_environment": "dev"}}, "Solacom.InfoHub.EventReceiver.Web.API": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}, "applicationUrl": "http://localhost:5000"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "httpPort": 62081}}}