{"attributes":{"fields":"[{\"name\":\"_id\",\"type\":\"string\",\"esTypes\":[\"_id\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":false},{\"name\":\"_index\",\"type\":\"string\",\"esTypes\":[\"_index\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":false},{\"name\":\"_score\",\"type\":\"number\",\"count\":0,\"scripted\":false,\"searchable\":false,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"_source\",\"type\":\"_source\",\"esTypes\":[\"_source\"],\"count\":0,\"scripted\":false,\"searchable\":false,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"_type\",\"type\":\"string\",\"esTypes\":[\"_type\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":false},{\"name\":\"abandonedState\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":1,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"adminCall\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"adminEmergencyCall\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"agentAnsweredLessThan10s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"agentAnsweredLessThan15s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"agentAnsweredLessThan40s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"agentAnsweredMoreThan40s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"agentName\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"agentName.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"agentName\",\"subType\":\"multi\"},{\"name\":\"agentTimeToAnswerInSeconds\",\"type\":\"number\",\"esTypes\":[\"double\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"ani\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"ani.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"ani\",\"subType\":\"multi\"},{\"name\":\"answeredBySystem\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"answeredLessThan10s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"answeredLessThan15s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"answeredLessThan40s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"answeredMoreThan40s\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callAnswered\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callArrived\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callDetailsId\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":2,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callDetailsTimeStamp\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callPresented\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callReleased\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"callbacknumber\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"callbacknumber.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"callbacknumber\",\"subType\":\"multi\"},{\"name\":\"callid\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"callid.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"callid\",\"subType\":\"multi\"},{\"name\":\"callmobilitytype\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":1,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"callmobilitytype.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"callmobilitytype\",\"subType\":\"multi\"},{\"name\":\"callstate\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":2,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"callstate.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"callstate\",\"subType\":\"multi\"},{\"name\":\"calltype\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":1,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"calltype.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"calltype\",\"subType\":\"multi\"},{\"name\":\"completedState\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"emergencyCall\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":2,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"endtime\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"finalCos\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"finalCos.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"finalCos\",\"subType\":\"multi\"},{\"name\":\"id\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"inProgressState\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"incidentid\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"incidentid.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"incidentid\",\"subType\":\"multi\"},{\"name\":\"isAdmin\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"isAdminEmergency\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"isEmergency\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"isOutbound\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"isTandem\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"isTransferred\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"isUnknownType\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"landlineType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"location\",\"type\":\"geo_point\",\"esTypes\":[\"geo_point\"],\"count\":2,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"loginId\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"loginId.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"loginId\",\"subType\":\"multi\"},{\"name\":\"notFoundType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"originalCos\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"originalCos.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"originalCos\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.agency\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.agency.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.agency\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.agent\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.agent.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.agent\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.ani\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.ani.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.ani\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.aniDomain\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.aniDomain.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.aniDomain\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.aniTranslated\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.aniTranslated.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.aniTranslated\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.attempt\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.attempt.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.attempt\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.callerName\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.callerName.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.callerName\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.callerNameTranslated\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.callerNameTranslated.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.callerNameTranslated\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.dnis\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.dnis.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.dnis\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.dnisTranslated\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.dnisTranslated.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.dnisTranslated\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.id\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"outboundCalls.mediaLabel\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.mediaLabel.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.mediaLabel\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.method\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.method.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.method\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.outboundTarget\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.outboundTarget.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.outboundTarget\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.pani\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.pani.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.pani\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.priority\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.priority.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.priority\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.reason\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.reason.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.reason\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.rule\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.rule.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.rule\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.targetName\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.targetName.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.targetName\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.targetType\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"outboundCalls.targetType.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"outboundCalls.targetType\",\"subType\":\"multi\"},{\"name\":\"outboundCalls.timeStamp\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"psapName\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"psapName.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"psapName\",\"subType\":\"multi\"},{\"name\":\"rTTType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"routesAndTransfers.agency\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"routesAndTransfers.agency.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"routesAndTransfers.agency\",\"subType\":\"multi\"},{\"name\":\"routesAndTransfers.agent\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"routesAndTransfers.agent.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"routesAndTransfers.agent\",\"subType\":\"multi\"},{\"name\":\"routesAndTransfers.attemptnumber\",\"type\":\"number\",\"esTypes\":[\"integer\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"routesAndTransfers.callpriority\",\"type\":\"number\",\"esTypes\":[\"integer\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"routesAndTransfers.id\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"routesAndTransfers.isAnswered\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"routesAndTransfers.isTransfer\",\"type\":\"boolean\",\"esTypes\":[\"boolean\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"routesAndTransfers.phone\",\"type\":\"string\",\"esTypes\":[\"text\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":false,\"readFromDocValues\":false},{\"name\":\"routesAndTransfers.phone.keyword\",\"type\":\"string\",\"esTypes\":[\"keyword\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true,\"parent\":\"routesAndTransfers.phone\",\"subType\":\"multi\"},{\"name\":\"routesAndTransfers.timeStamp\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"sMSType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"starttime\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"tDDType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"tandemCall\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"timeStamp\",\"type\":\"date\",\"esTypes\":[\"date\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"timeToAnswerInSeconds\",\"type\":\"number\",\"esTypes\":[\"double\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"totalCallTimeInSeconds\",\"type\":\"number\",\"esTypes\":[\"double\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"unknownCall\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"unknownType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"voipType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true},{\"name\":\"wirelessType\",\"type\":\"number\",\"esTypes\":[\"float\"],\"count\":0,\"scripted\":false,\"searchable\":true,\"aggregatable\":true,\"readFromDocValues\":true}]","timeFieldName":"timeStamp","title":"horry_callsummary"},"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","migrationVersion":{"index-pattern":"6.5.0"},"references":[],"type":"index-pattern","updated_at":"2020-03-02T14:19:01.750Z","version":"WzE0ODMsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"Agent Grade of Service","uiStateJSON":"{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":null,\"direction\":null}}}}","version":1,"visState":"{\"title\":\"Agent Grade of Service\",\"type\":\"enhanced-table\",\"params\":{\"perPage\":10,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"showTotal\":false,\"totalFunc\":\"sum\",\"computedColumns\":[{\"label\":\"Grade of Service 10 sec\",\"formula\":\"col1/(col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:1178\"},{\"label\":\"Grade of Service 15 Sec\",\"formula\":\"col2/(col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:1187\"},{\"label\":\"Grade of Service 40 Sec\",\"formula\":\"col3/(col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:1206\"}],\"computedColsPerSplitCol\":false,\"hideExportLinks\":false,\"showFilterBar\":false,\"filterCaseSensitive\":false,\"filterBarHideable\":false,\"filterAsYouType\":false,\"filterTermsSeparately\":false,\"filterHighlightResults\":false,\"filterBarWidth\":\"25%\",\"dimensions\":{\"metric\":[{\"accessor\":2,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":3,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":4,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":5,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":6,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"}],\"split_row\":[{\"accessor\":0,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}],\"bucket\":[{\"accessor\":1,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}]},\"linesComputedFilter\":\"\",\"hiddenColumns\":\"1,2,3,4\"},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredLessThan10s\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredLessThan15s\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredLessThan40s\"}},{\"id\":\"4\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredMoreThan40s\"}},{\"id\":\"5\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"emergencyCall\",\"customLabel\":\"911 Calls\"}},{\"id\":\"6\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"split\",\"params\":{\"field\":\"psapName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"PSAP\",\"row\":true}},{\"id\":\"7\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"agentName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":100,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\"}}]}"},"id":"a583fb00-1c46-11ea-a14b-1711242a6c48","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"visualization","updated_at":"2019-12-13T16:29:01.354Z","version":"WzEwNzcsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":true,\"type\":\"phrase\",\"key\":\"callDetailsId\",\"value\":\"00000000-0000-0000-0000-000000000000\",\"params\":{\"query\":\"00000000-0000-0000-0000-000000000000\"},\"disabled\":false,\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match\":{\"callDetailsId\":{\"query\":\"00000000-0000-0000-0000-000000000000\",\"type\":\"phrase\"}}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"PSAP Grade of Service","uiStateJSON":"{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":null,\"direction\":null}}}}","version":1,"visState":"{\"title\":\"PSAP Grade of Service\",\"type\":\"enhanced-table\",\"params\":{\"computedColsPerSplitCol\":false,\"computedColumns\":[{\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"enabled\":true,\"format\":\"number\",\"formula\":\"col1+ col2\",\"label\":\"Total Calls\",\"pattern\":\"0,0\",\"template\":\"{{value}}\",\"$$hashKey\":\"object:8924\"},{\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"enabled\":true,\"format\":\"number\",\"formula\":\"col1 /( col1+col2)\",\"label\":\"% 911 Calls\",\"pattern\":\"0.000%\",\"template\":\"{{value}}\",\"$$hashKey\":\"object:8925\"},{\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"enabled\":true,\"format\":\"number\",\"formula\":\"col3/(col5+col6)\",\"label\":\"Grade of Service 10 Sec\",\"pattern\":\"0.000%\",\"template\":\"{{value}}\",\"$$hashKey\":\"object:8926\"},{\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"enabled\":true,\"format\":\"number\",\"formula\":\"col4/(col5+col6)\",\"label\":\"Grade of Service 15 Sec\",\"pattern\":\"0.000%\",\"template\":\"{{value}}\",\"$$hashKey\":\"object:8927\"},{\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"enabled\":true,\"format\":\"number\",\"formula\":\"col5/(col5+col6)\",\"label\":\"Grade of Service 40 Sec\",\"pattern\":\"0.000%\",\"template\":\"{{value}}\",\"$$hashKey\":\"object:8928\"}],\"dimensions\":{\"metric\":[{\"accessor\":1,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":2,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":3,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":4,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":5,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":6,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"}],\"bucket\":[{\"accessor\":0,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}]},\"filterAsYouType\":false,\"filterBarHideable\":false,\"filterBarWidth\":\"25%\",\"filterCaseSensitive\":false,\"filterHighlightResults\":false,\"filterTermsSeparately\":false,\"hiddenColumns\":\"1,2,3,4,5,6\",\"hideExportLinks\":false,\"perPage\":5,\"showFilterBar\":false,\"showMetricsAtAllLevels\":false,\"showPartialRows\":false,\"showTotal\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"totalFunc\":\"sum\"},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"emergencyCall\",\"customLabel\":\"911 Calls\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"psapName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"PSAP Name\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"adminCall\",\"customLabel\":\"Admin Calls\"}},{\"id\":\"4\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredLessThan10s\",\"customLabel\":\"<10Sec\"}},{\"id\":\"5\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredLessThan15s\",\"customLabel\":\"<15Sec\"}},{\"id\":\"6\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredLessThan40s\",\"customLabel\":\"<40 Sec\"}},{\"id\":\"7\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"agentAnsweredMoreThan40s\",\"customLabel\":\">40Sec\"}}]}"},"id":"f222b930-f4e9-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"visualization","updated_at":"2019-12-13T20:37:35.331Z","version":"WzExMTMsMV0="}
{"attributes":{"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[]}"},"optionsJSON":"{\"hidePanelTitles\":false,\"useMargins\":true}","panelsJSON":"[{\"gridData\":{\"x\":0,\"y\":10,\"w\":48,\"h\":46,\"i\":\"4\"},\"version\":\"7.3.2\",\"panelIndex\":\"4\",\"embeddableConfig\":{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":1,\"direction\":\"desc\"}}}},\"panelRefName\":\"panel_0\"},{\"gridData\":{\"x\":0,\"y\":0,\"w\":48,\"h\":10,\"i\":\"6\"},\"version\":\"7.3.2\",\"panelIndex\":\"6\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]","refreshInterval":{"pause":true,"value":0},"timeFrom":"2019-11-05T05:00:00.000Z","timeRestore":true,"timeTo":"2019-11-15T05:00:00.000Z","title":"Horry County Grade of Service","version":1},"id":"54252e20-f44e-11e9-b415-af25c4bbf169","migrationVersion":{"dashboard":"7.3.0"},"references":[{"id":"a583fb00-1c46-11ea-a14b-1711242a6c48","name":"panel_0","type":"visualization"},{"id":"f222b930-f4e9-11e9-b415-af25c4bbf169","name":"panel_1","type":"visualization"}],"type":"dashboard","updated_at":"2020-02-27T16:39:02.110Z","version":"WzE0NzgsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[{\"$state\":{\"store\":\"appState\"},\"meta\":{\"alias\":null,\"disabled\":false,\"key\":\"callDetailsId\",\"negate\":false,\"params\":{\"query\":\"00000000-0000-0000-0000-000000000000\"},\"type\":\"phrase\",\"value\":\"00000000-0000-0000-0000-000000000000\",\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match\":{\"callDetailsId\":{\"query\":\"00000000-0000-0000-0000-000000000000\",\"type\":\"phrase\"}}}},{\"$state\":{\"store\":\"appState\"},\"meta\":{\"alias\":null,\"disabled\":false,\"key\":\"isUnknownType\",\"negate\":false,\"params\":{\"query\":false},\"type\":\"phrase\",\"value\":\"false\",\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index\"},\"query\":{\"match\":{\"isUnknownType\":{\"query\":false,\"type\":\"phrase\"}}}},{\"$state\":{\"store\":\"appState\"},\"meta\":{\"alias\":null,\"disabled\":false,\"key\":\"isAdminEmergency\",\"negate\":false,\"params\":{\"query\":false},\"type\":\"phrase\",\"value\":\"false\",\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[2].meta.index\"},\"query\":{\"match\":{\"isAdminEmergency\":{\"query\":false,\"type\":\"phrase\"}}}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"Caller Count","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"Caller Count\",\"type\":\"metric\",\"params\":{\"metric\":{\"percentageMode\":false,\"useRanges\":false,\"colorSchema\":\"Green to Red\",\"metricColorMode\":\"Labels\",\"colorsRange\":[{\"type\":\"range\",\"from\":0,\"to\":25},{\"type\":\"range\",\"from\":25,\"to\":50},{\"type\":\"range\",\"from\":50,\"to\":75},{\"type\":\"range\",\"from\":75,\"to\":100},{\"type\":\"range\",\"from\":100,\"to\":125},{\"type\":\"range\",\"from\":125,\"to\":150},{\"type\":\"range\",\"from\":150,\"to\":175},{\"type\":\"range\",\"from\":175,\"to\":200},{\"type\":\"range\",\"from\":200,\"to\":225}],\"labels\":{\"show\":true},\"invertColors\":false,\"style\":{\"bgFill\":\"#000\",\"bgColor\":false,\"labelColor\":true,\"subText\":\"\",\"fontSize\":17}},\"dimensions\":{\"metrics\":[{\"type\":\"vis_dimension\",\"accessor\":0,\"format\":{\"id\":\"number\",\"params\":{}}},{\"type\":\"vis_dimension\",\"accessor\":1,\"format\":{\"id\":\"number\",\"params\":{}}},{\"type\":\"vis_dimension\",\"accessor\":2,\"format\":{\"id\":\"number\",\"params\":{}}}]},\"addTooltip\":true,\"addLegend\":false,\"type\":\"metric\"},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"adminCall\",\"customLabel\":\"Admin Calls\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"emergencyCall\",\"customLabel\":\"911 Calls\"}},{\"id\":\"5\",\"enabled\":false,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"adminEmergencyCall\",\"customLabel\":\"Incoming Admin Emergency Calls\"}},{\"id\":\"4\",\"enabled\":false,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"tandemCall\",\"customLabel\":\"Incoming Tandem Calls\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}}]}"},"id":"f840f660-f2ff-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[2].meta.index","type":"index-pattern"}],"type":"visualization","updated_at":"2020-03-09T15:28:30.940Z","version":"WzE1MDksMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"Mobile Calls Location","uiStateJSON":"{\"mapZoom\":9,\"mapCenter\":[33.73119253613478,-78.70605468750001]}","version":1,"visState":"{\"title\":\"Mobile Calls Location\",\"type\":\"tile_map\",\"params\":{\"colorSchema\":\"Yellow to Red\",\"mapType\":\"Heatmap\",\"isDesaturated\":true,\"addTooltip\":true,\"heatClusterSize\":1.7,\"legendPosition\":\"bottomright\",\"mapZoom\":2,\"mapCenter\":[0,0],\"wms\":{\"enabled\":false,\"options\":{\"format\":\"image/png\",\"transparent\":true}},\"dimensions\":{\"metric\":{\"accessor\":2,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"count\"},\"geohash\":{\"accessor\":1,\"format\":{\"id\":\"string\"},\"params\":{\"precision\":5,\"useGeocentroid\":true},\"aggType\":\"geohash_grid\"},\"geocentroid\":{\"accessor\":3,\"format\":{\"id\":\"string\"},\"params\":{},\"aggType\":\"geo_centroid\"}}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"geohash_grid\",\"schema\":\"segment\",\"params\":{\"field\":\"location\",\"autoPrecision\":true,\"precision\":5,\"useGeocentroid\":true,\"isFilteredByCollar\":true,\"mapZoom\":9,\"mapCenter\":{\"lon\":-78.70605468750001,\"lat\":33.73119253613478},\"mapBounds\":{\"bottom_right\":{\"lat\":33.29150775159364,\"lon\":-77.43988037109375},\"top_left\":{\"lat\":34.168635904722734,\"lon\":-79.97222900390625}}}}]}"},"id":"99ed5b10-f30b-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"visualization","updated_at":"2020-02-27T16:04:28.206Z","version":"WzE0NjcsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"Average 911 Answer Time","uiStateJSON":"{\"vis\":{\"defaultColors\":{\"0 - 5\":\"rgb(0,104,55)\",\"5 - 10\":\"rgb(135,203,103)\",\"10 - 15\":\"rgb(255,255,190)\",\"15 - 20\":\"rgb(249,142,82)\",\"20 - 25\":\"rgb(165,0,38)\"}}}","version":1,"visState":"{\"title\":\"Average 911 Answer Time\",\"type\":\"goal\",\"params\":{\"addTooltip\":true,\"addLegend\":false,\"isDisplayWarning\":false,\"type\":\"gauge\",\"gauge\":{\"verticalSplit\":false,\"autoExtend\":false,\"percentageMode\":false,\"gaugeType\":\"Arc\",\"gaugeStyle\":\"Full\",\"backStyle\":\"Full\",\"orientation\":\"vertical\",\"useRanges\":false,\"colorSchema\":\"Green to Red\",\"gaugeColorMode\":\"None\",\"colorsRange\":[{\"from\":0,\"to\":5},{\"from\":5,\"to\":10},{\"from\":10,\"to\":15},{\"from\":15,\"to\":20},{\"from\":20,\"to\":25}],\"invertColors\":false,\"labels\":{\"show\":true,\"color\":\"black\"},\"scale\":{\"show\":true,\"labels\":false,\"color\":\"rgba(105,112,125,0.2)\",\"width\":2},\"type\":\"meter\",\"style\":{\"bgFill\":\"rgba(105,112,125,0.2)\",\"bgColor\":false,\"labelColor\":false,\"subText\":\"\",\"fontSize\":60}},\"dimensions\":{\"x\":null,\"y\":[{\"accessor\":0,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"avg\"}]}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"avg\",\"schema\":\"metric\",\"params\":{\"field\":\"timeToAnswerInSeconds\",\"customLabel\":\"Benchmark=10 Sec\"}}]}"},"id":"839510d0-f6ec-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"}],"type":"visualization","updated_at":"2019-12-18T16:43:18.132Z","version":"WzExOTYsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"},"title":"Call Count","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"Call Count\",\"type\":\"metrics\",\"params\":{\"id\":\"61ca57f0-469d-11e7-af02-69e470af7417\",\"type\":\"timeseries\",\"series\":[{\"id\":\"61ca57f1-469d-11e7-af02-69e470af7417\",\"color\":\"#68BC00\",\"split_mode\":\"everything\",\"metrics\":[{\"id\":\"61ca57f2-469d-11e7-af02-69e470af7417\",\"type\":\"count\"}],\"separate_axis\":0,\"axis_position\":\"right\",\"formatter\":\"number\",\"chart_type\":\"line\",\"line_width\":\"2\",\"point_size\":\"4\",\"fill\":\"0\",\"stacked\":\"none\",\"filter\":{\"query\":\"callDetailsId:00000000-0000-0000-0000-000000000000\",\"language\":\"kuery\"},\"label\":\"Total Incoming Calls\",\"split_color_mode\":\"gradient\",\"steps\":0},{\"id\":\"c0469410-1dd2-11ea-9c9e-bd6eecb2747e\",\"color\":\"rgba(248,7,11,1)\",\"split_mode\":\"terms\",\"metrics\":[{\"id\":\"c0469411-1dd2-11ea-9c9e-bd6eecb2747e\",\"type\":\"sum\",\"field\":\"emergencyCall\"}],\"separate_axis\":0,\"axis_position\":\"right\",\"formatter\":\"number\",\"chart_type\":\"line\",\"line_width\":\"2\",\"point_size\":\"4\",\"fill\":\"0\",\"stacked\":\"none\",\"label\":\"911 Calls\",\"filter\":{\"query\":\"callDetailsId:00000000-0000-0000-0000-000000000000\",\"language\":\"kuery\"}},{\"id\":\"f8e64680-1dd2-11ea-9c9e-bd6eecb2747e\",\"color\":\"rgba(20,7,248,1)\",\"split_mode\":\"everything\",\"metrics\":[{\"id\":\"f8e64681-1dd2-11ea-9c9e-bd6eecb2747e\",\"type\":\"sum\",\"field\":\"adminCall\"}],\"separate_axis\":0,\"axis_position\":\"right\",\"formatter\":\"number\",\"chart_type\":\"line\",\"line_width\":\"2\",\"point_size\":\"4\",\"fill\":\"0\",\"stacked\":\"none\",\"label\":\"Admin Calls\",\"filter\":{\"query\":\"callDetailsId:00000000-0000-0000-0000-000000000000\",\"language\":\"kuery\"}}],\"time_field\":\"\",\"index_pattern\":\"horry_callsummary\",\"interval\":\"1d\",\"axis_position\":\"left\",\"axis_formatter\":\"number\",\"axis_scale\":\"normal\",\"show_legend\":1,\"show_grid\":1,\"default_index_pattern\":\"callsummary\",\"default_timefield\":\"timeStamp\",\"ignore_global_filter\":0,\"background_color\":null,\"drop_last_bucket\":1},\"aggs\":[]}"},"id":"55caa3f0-1dd3-11ea-a14b-1711242a6c48","migrationVersion":{"visualization":"7.3.1"},"references":[],"type":"visualization","updated_at":"2020-02-27T15:59:58.016Z","version":"WzE0NjUsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"},"title":"Grade of Service - 15 seconds","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"Grade of Service - 15 seconds\",\"type\":\"vega\",\"params\":{\"spec\":\"{\\r\\n\\t\\\"$schema\\\": \\\"https://vega.github.io/schema/vega/v4.3.0.json\\\",\\r\\n\\t\\\"data\\\": [{\\r\\n\\t\\t\\t\\\"%context%\\\": false,\\r\\n\\t\\t\\t\\\"name\\\": \\\"liveCalls\\\",\\r\\n\\t\\t\\t\\\"url\\\": {\\r\\n\\t\\t\\t\\t\\\"index\\\": \\\"horrycounty_callsummary\\\",\\r\\n\\t\\t\\t\\t\\\"body\\\": {\\r\\n\\t\\t\\t\\t\\t\\\"size\\\": 10,\\r\\n\\t\\t\\t\\t\\t\\\"_source\\\": [\\\"callDetailsId\\\", \\\"emergencyCall\\\", \\\"answeredLessThan10s\\\"],\\r\\n\\t\\t\\t\\t\\t\\\"query\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\\"bool\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\\"must\\\": [{\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\\"match\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\\"callDetailsId\\\": \\\"00000000-0000-0000-0000-000000000000\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t\\t\\t}]\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t},\\r\\n\\t\\t\\t\\t\\t\\\"aggs\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\\"total911\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\\"sum\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\\"field\\\": \\\"emergencyCall\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t\\t},\\r\\n\\t\\t\\t\\t\\t\\t\\\"total10s\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\\"sum\\\": {\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\\"field\\\": \\\"answeredLessThan10s\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t},\\r\\n\\t\\t\\t\\\"format\\\": {\\r\\n\\t\\t\\t\\t\\\"property\\\": \\\"aggregations\\\"\\r\\n\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\r\\n\\t],\\r\\n\\r\\n\\t\\\"marks\\\": [{\\r\\n\\t\\t\\\"type\\\": \\\"text\\\",\\r\\n\\t\\t\\\"from\\\": {\\r\\n\\t\\t\\t\\\"data\\\": \\\"liveCalls\\\"\\r\\n\\t\\t},\\r\\n\\t\\t\\\"encode\\\": {\\r\\n\\t\\t\\t\\\"update\\\": {\\r\\n\\t\\t\\t\\t\\\"text\\\": {\\r\\n\\t\\t\\t\\t\\t\\\"signal\\\": \\\"format(datum.total10s.value * 100 / datum.total911.value, '.2f') + '%'\\\"\\r\\n\\t\\t\\t\\t},\\r\\n\\t\\t\\t\\t\\\"align\\\": {\\r\\n\\t\\t\\t\\t\\t\\\"value\\\": \\\"center\\\"\\r\\n\\t\\t\\t\\t},\\r\\n\\t\\t\\t\\t\\\"baseline\\\": {\\r\\n\\t\\t\\t\\t\\t\\\"value\\\": \\\"middle\\\"\\r\\n\\t\\t\\t\\t},\\r\\n\\t\\t\\t\\t\\\"fontSize\\\": {\\r\\n\\t\\t\\t\\t\\t\\\"value\\\": 60\\r\\n\\t\\t\\t\\t},\\r\\n\\t\\t\\t\\t\\\"fontWeight\\\": {\\r\\n\\t\\t\\t\\t\\t\\\"value\\\": 400\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}]\\r\\n}\"},\"aggs\":[]}"},"id":"0f8fbb20-5805-11ea-8997-19ee3c16ad0b","migrationVersion":{"visualization":"7.3.1"},"references":[],"type":"visualization","updated_at":"2020-02-27T15:51:51.558Z","version":"WzE0NjMsMV0="}
{"attributes":{"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[]}"},"optionsJSON":"{\"hidePanelTitles\":false,\"useMargins\":true}","panelsJSON":"[{\"embeddableConfig\":{},\"gridData\":{\"h\":8,\"i\":\"2\",\"w\":20,\"x\":0,\"y\":0},\"panelIndex\":\"2\",\"title\":\"Incoming Calls\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_0\"},{\"embeddableConfig\":{\"mapCenter\":[33.813384329112814,-78.96148681640626],\"mapZoom\":9},\"gridData\":{\"h\":10,\"i\":\"3\",\"w\":20,\"x\":0,\"y\":8},\"panelIndex\":\"3\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_1\"},{\"embeddableConfig\":{},\"gridData\":{\"h\":8,\"i\":\"4\",\"w\":15,\"x\":20,\"y\":0},\"panelIndex\":\"4\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_2\"},{\"embeddableConfig\":{},\"gridData\":{\"h\":10,\"i\":\"8\",\"w\":28,\"x\":20,\"y\":8},\"panelIndex\":\"8\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_3\"},{\"embeddableConfig\":{},\"gridData\":{\"h\":8,\"i\":\"9\",\"w\":13,\"x\":35,\"y\":0},\"panelIndex\":\"9\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_4\"}]","refreshInterval":{"pause":false,"value":5000},"timeFrom":"2019-11-05T05:00:00.000Z","timeRestore":true,"timeTo":"2019-11-15T05:00:00.000Z","title":"Horry County Essentials Dashboard","version":1},"id":"cac3a7b0-f44d-11e9-b415-af25c4bbf169","migrationVersion":{"dashboard":"7.3.0"},"references":[{"id":"f840f660-f2ff-11e9-b415-af25c4bbf169","name":"panel_0","type":"visualization"},{"id":"99ed5b10-f30b-11e9-b415-af25c4bbf169","name":"panel_1","type":"visualization"},{"id":"839510d0-f6ec-11e9-b415-af25c4bbf169","name":"panel_2","type":"visualization"},{"id":"55caa3f0-1dd3-11ea-a14b-1711242a6c48","name":"panel_3","type":"visualization"},{"id":"0f8fbb20-5805-11ea-8997-19ee3c16ad0b","name":"panel_4","type":"visualization"}],"type":"dashboard","updated_at":"2020-03-09T14:59:48.946Z","version":"WzE1MDgsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":true,\"type\":\"phrase\",\"key\":\"callDetailsId\",\"value\":\"00000000-0000-0000-0000-000000000000\",\"params\":{\"query\":\"00000000-0000-0000-0000-000000000000\"},\"disabled\":false,\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match\":{\"callDetailsId\":{\"query\":\"00000000-0000-0000-0000-000000000000\",\"type\":\"phrase\"}}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"PSAP Call Distribution","uiStateJSON":"{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":null,\"direction\":null}}}}","version":1,"visState":"{\"title\":\"PSAP Call Distribution\",\"type\":\"enhanced-table\",\"params\":{\"perPage\":5,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"showTotal\":false,\"totalFunc\":\"sum\",\"computedColumns\":[{\"label\":\"% Admin Calls\",\"formula\":\"col1/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.0000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:50228\"},{\"label\":\"% 911 Calls\",\"formula\":\"col2 /(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"'0.000%'\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:50229\"},{\"label\":\"% Unknown Calls\",\"formula\":\"col3/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:50230\"},{\"label\":\"% Admin Emergency Calls\",\"formula\":\"col4/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true,\"$$hashKey\":\"object:50231\"}],\"computedColsPerSplitCol\":false,\"hideExportLinks\":false,\"showFilterBar\":false,\"filterCaseSensitive\":false,\"filterBarHideable\":false,\"filterAsYouType\":false,\"filterTermsSeparately\":false,\"filterHighlightResults\":false,\"filterBarWidth\":\"25%\",\"dimensions\":{\"metric\":[{\"accessor\":1,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":2,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":3,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":4,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"}],\"bucket\":[{\"accessor\":0,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}]}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"adminCall\",\"customLabel\":\"Admin Calls\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"emergencyCall\",\"customLabel\":\"911 Calls\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"psapName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"PSAP\"}},{\"id\":\"4\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"unknownCall\",\"customLabel\":\"Unknown Calls\"}},{\"id\":\"5\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"adminEmergencyCall\",\"customLabel\":\"Admin Emergency Calls\"}}]}"},"id":"ce9b85d0-f2fc-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"visualization","updated_at":"2019-12-17T19:19:02.513Z","version":"WzExODcsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":true,\"type\":\"phrase\",\"key\":\"callDetailsId\",\"value\":\"00000000-0000-0000-0000-000000000000\",\"params\":{\"query\":\"00000000-0000-0000-0000-000000000000\"},\"disabled\":false,\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match\":{\"callDetailsId\":{\"query\":\"00000000-0000-0000-0000-000000000000\",\"type\":\"phrase\"}}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"Class of Service Distribution","uiStateJSON":"{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":null,\"direction\":null}}}}","version":1,"visState":"{\"title\":\"Class of Service Distribution\",\"type\":\"enhanced-table\",\"params\":{\"perPage\":5,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"showTotal\":false,\"totalFunc\":\"sum\",\"computedColumns\":[{\"label\":\"% Landline Calls\",\"formula\":\"col1/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true},{\"label\":\"% Wireless Calls\",\"formula\":\"col2/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true},{\"label\":\"% Voip Calls\",\"formula\":\"col3/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true},{\"label\":\"% Unknown Calls\",\"formula\":\"col4/(col1+col2+col3+col4)\",\"format\":\"number\",\"pattern\":\"0.000%\",\"datePattern\":\"MMMM Do YYYY, HH:mm:ss.SSS\",\"alignment\":\"left\",\"applyAlignmentOnTitle\":true,\"applyAlignmentOnTotal\":true,\"applyTemplate\":false,\"applyTemplateOnTotal\":true,\"template\":\"{{value}}\",\"enabled\":true}],\"computedColsPerSplitCol\":false,\"hideExportLinks\":false,\"showFilterBar\":false,\"filterCaseSensitive\":false,\"filterBarHideable\":false,\"filterAsYouType\":false,\"filterTermsSeparately\":false,\"filterHighlightResults\":false,\"filterBarWidth\":\"25%\",\"dimensions\":{\"metric\":[{\"accessor\":1,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":2,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":3,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":4,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"}],\"bucket\":[{\"accessor\":0,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}]}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"landlineType\",\"customLabel\":\"Landline Calls\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"wirelessType\",\"customLabel\":\"Wireless Calls\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"voipType\",\"customLabel\":\"Voip Calls\"}},{\"id\":\"4\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"unknownType\",\"customLabel\":\"Unknown\"}},{\"id\":\"5\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"psapName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"PSAP Name\"}}]}"},"id":"3d38cbc0-f301-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"visualization","updated_at":"2019-12-13T20:30:23.649Z","version":"WzExMDksMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":true,\"type\":\"phrase\",\"key\":\"callDetailsId\",\"value\":\"00000000-0000-0000-0000-000000000000\",\"params\":{\"query\":\"00000000-0000-0000-0000-000000000000\"},\"disabled\":false,\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match\":{\"callDetailsId\":{\"query\":\"00000000-0000-0000-0000-000000000000\",\"type\":\"phrase\"}}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"Transfer and Abandoned Calls","uiStateJSON":"{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":null,\"direction\":null}}}}","version":1,"visState":"{\"title\":\"Transfer and Abandoned Calls\",\"type\":\"enhanced-table\",\"params\":{\"perPage\":5,\"showPartialRows\":false,\"showMetricsAtAllLevels\":false,\"sort\":{\"columnIndex\":null,\"direction\":null},\"showTotal\":false,\"totalFunc\":\"sum\",\"computedColumns\":[],\"computedColsPerSplitCol\":false,\"hideExportLinks\":false,\"showFilterBar\":false,\"filterCaseSensitive\":false,\"filterBarHideable\":false,\"filterAsYouType\":false,\"filterTermsSeparately\":false,\"filterHighlightResults\":false,\"filterBarWidth\":\"25%\",\"dimensions\":{\"metric\":[{\"accessor\":1,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":2,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"},{\"accessor\":3,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"sum\"}],\"bucket\":[{\"accessor\":0,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}]}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"isTransferred\",\"customLabel\":\"Transferred Calls\"}},{\"id\":\"2\",\"enabled\":true,\"type\":\"sum\",\"schema\":\"metric\",\"params\":{\"field\":\"abandonedState\",\"customLabel\":\"Abandoned Calls\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"bucket\",\"params\":{\"field\":\"psapName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"PSAP Name\"}}]}"},"id":"5cf6e310-f447-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"visualization","updated_at":"2020-03-09T14:44:24.738Z","version":"WzE1MDUsMV0="}
{"attributes":{"description":"","kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[{\"meta\":{\"alias\":null,\"negate\":true,\"type\":\"phrase\",\"key\":\"callDetailsId\",\"value\":\"00000000-0000-0000-0000-000000000000\",\"params\":{\"query\":\"00000000-0000-0000-0000-000000000000\"},\"disabled\":false,\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index\"},\"query\":{\"match\":{\"callDetailsId\":{\"query\":\"00000000-0000-0000-0000-000000000000\",\"type\":\"phrase\"}}},\"$state\":{\"store\":\"appState\"}}],\"indexRefName\":\"kibanaSavedObjectMeta.searchSourceJSON.index\"}"},"title":"PSAP-Class of Service Distribution","uiStateJSON":"{}","version":1,"visState":"{\"title\":\"PSAP-Class of Service Distribution\",\"type\":\"pie\",\"params\":{\"type\":\"pie\",\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"bottom\",\"isDonut\":false,\"labels\":{\"show\":true,\"values\":true,\"last_level\":true,\"truncate\":100},\"dimensions\":{\"metric\":{\"accessor\":1,\"format\":{\"id\":\"number\"},\"params\":{},\"aggType\":\"count\"},\"buckets\":[{\"accessor\":0,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"},{\"accessor\":2,\"format\":{\"id\":\"terms\",\"params\":{\"id\":\"string\",\"otherBucketLabel\":\"Other\",\"missingBucketLabel\":\"Missing\"}},\"params\":{},\"aggType\":\"terms\"}]}},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"psapName.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"PSAP\"}},{\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"callmobilitytype.keyword\",\"orderBy\":\"1\",\"order\":\"desc\",\"size\":5,\"otherBucket\":false,\"otherBucketLabel\":\"Other\",\"missingBucket\":false,\"missingBucketLabel\":\"Missing\",\"customLabel\":\"Call Mobility\"}}]}"},"id":"c9d97980-f6e4-11e9-b415-af25c4bbf169","migrationVersion":{"visualization":"7.3.1"},"references":[{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.index","type":"index-pattern"},{"id":"f0d47470-1b8f-11ea-a14b-1711242a6c48","name":"kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index","type":"index-pattern"}],"type":"visualization","updated_at":"2019-12-13T20:31:19.744Z","version":"WzExMTAsMV0="}
{"attributes":{"description":"","hits":0,"kibanaSavedObjectMeta":{"searchSourceJSON":"{\"query\":{\"language\":\"kuery\",\"query\":\"\"},\"filter\":[]}"},"optionsJSON":"{\"hidePanelTitles\":false,\"useMargins\":true}","panelsJSON":"[{\"embeddableConfig\":{},\"gridData\":{\"h\":10,\"i\":\"4\",\"w\":48,\"x\":0,\"y\":0},\"panelIndex\":\"4\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_0\"},{\"embeddableConfig\":{},\"gridData\":{\"h\":14,\"i\":\"5\",\"w\":28,\"x\":20,\"y\":10},\"panelIndex\":\"5\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_1\"},{\"embeddableConfig\":{\"vis\":{\"params\":{\"sort\":{\"columnIndex\":0,\"direction\":\"asc\"}}}},\"gridData\":{\"h\":10,\"i\":\"6\",\"w\":48,\"x\":0,\"y\":24},\"panelIndex\":\"6\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_2\"},{\"embeddableConfig\":{},\"gridData\":{\"h\":14,\"i\":\"7\",\"w\":20,\"x\":0,\"y\":10},\"panelIndex\":\"7\",\"title\":\"Class of Service Distribution\",\"version\":\"7.3.2\",\"panelRefName\":\"panel_3\"}]","refreshInterval":{"pause":true,"value":5000},"timeFrom":"2019-11-05T05:00:00.000Z","timeRestore":true,"timeTo":"2019-11-15T05:00:00.000Z","title":"Horry County Call Distribution","version":1},"id":"d707a660-f44e-11e9-b415-af25c4bbf169","migrationVersion":{"dashboard":"7.3.0"},"references":[{"id":"ce9b85d0-f2fc-11e9-b415-af25c4bbf169","name":"panel_0","type":"visualization"},{"id":"3d38cbc0-f301-11e9-b415-af25c4bbf169","name":"panel_1","type":"visualization"},{"id":"5cf6e310-f447-11e9-b415-af25c4bbf169","name":"panel_2","type":"visualization"},{"id":"c9d97980-f6e4-11e9-b415-af25c4bbf169","name":"panel_3","type":"visualization"}],"type":"dashboard","updated_at":"2020-03-09T14:44:58.768Z","version":"WzE1MDYsMV0="}